[tool.poetry]
name = "frappe-manager"
version = "0.17.0"
license = "MIT"
repository = "https://github.com/rtcamp/frappe-manager"
description = "A CLI tool based on Docker Compose to easily manage Frappe based projects. As of now, only suitable for development in local machines running on Mac and Linux based OS."
authors = ["rtCamp <<EMAIL>>"]
maintainers = ["<PERSON><PERSON> <<EMAIL>>"]
documentation = "https://github.com/rtcamp/frappe-manager/wiki"
readme = "README.md"

[tool.ruff]
line-length = 120
indent-width = 4
target-version = "py310"

[tool.ruff.lint]
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
select = ["E", "F", "I", "B", "C", "W", "UP", "N", "YTT", "S", "BLE", "FBT", "A", "COM", "C4", "DTZ", "T10", "EM", "EXE", "ISC", "ICN", "G", "INP", "PIE", "T20", "PYI", "PT", "Q", "RSE", "RET", "SLF", "SIM", "TID", "TCH", "ARG", "PTH", "ERA", "PD", "PGH", "PL", "TRY", "RUF"]
ignore = [
    "F841",    # Unused variables
    "E501",    # Line too long (handled by formatter)
    "C901",    # Function is too complex
    "PLR0913", # Too many arguments to function call
]
fixable = ["ALL"]
unfixable = []

[tool.ruff.format]
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
quote-style = "preserve"

[tool.ruff.lint.isort]
force-single-line = false
known-first-party = ["frappe_manager"]

[tool.poetry.urls]
"Bug Tracker" = "https://github.com/rtcamp/frappe-manager/issues"

[tool.poetry.scripts]
fm = "frappe_manager.main:cli_entrypoint"

[tool.poetry.dependencies]
python = "^3.10"
typer = "^0.15.4"
requests = "^2.31.0"
psutil = "^5.9.6"
configargparse = "!=1.7"
ruamel-yaml = "^0.18.5"
tomlkit = "^0.12.3"
certbot = "^2.9.0"
pydantic = "^2.6.4"
email-validator = "^2.1.1"
jinja2 = "^3.1.3"
certbot-dns-cloudflare = "^2.10.0"
ngrok = "^1.4.0"
passlib = "^1.7.4"
inquirerpy = "^0.3.4"

[tool.pyright]
reportOptionalMemberAccess = false

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
