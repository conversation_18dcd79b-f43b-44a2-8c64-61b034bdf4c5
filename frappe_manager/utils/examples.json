{"create": {"examples": [{"desc": "Install Frappe with the stable {default_version} branch.", "code": ""}, {"desc": "Install Frappe with the develop branch.", "code": " --frappe-branch develop"}, {"desc": "Install Frappe, ERPNext, and HRMS with the stable {default_version} branch.", "code": " --apps erpnext --apps hrms"}, {"desc": "Install Frappe[{default_version}], ERPNext[{default_version}], and HRMS[develop].", "code": " --apps erpnext --apps hrms:develop"}, {"desc": "Use Frappe production mode based bench.", "code": " -e prod"}, {"desc": "Enable Admin <PERSON>.", "code": " --admin-tools enable"}, {"desc": "Enable HTTPS using HTTP01 Let's Encrypt challenge certificate.", "code": " --ssl letsencrypt --letsencrypt-preferred-challenge http01 --letsencrypt-email <EMAIL>"}, {"desc": "Enable HTTPS using DNS01 Let's Encrypt challenge certificate.", "code": " --ssl letsencrypt --letsencrypt-preferred-challenge dns01"}]}, "delete": {"examples": [{"desc": "Delete bench {benchname}", "code": ""}]}, "stop": {"examples": [{"desc": "Stop bench {benchname}", "code": ""}]}, "info": {"examples": [{"desc": "Show information about bench {benchname}", "code": ""}]}, "list": {"examples": [{"desc": "List all available benches", "code": ""}]}, "code": {"examples": [{"desc": "Open the bench {benchname} in default editor", "code": ""}, {"desc": "Open the bench {benchname} with VSCode specifically", "code": " --editor vscode"}, {"desc": "Open the bench {benchname} with Cursor editor", "code": " --editor cursor"}, {"desc": "Open the bench {benchname} with Windsurf editor", "code": " --editor windsurf"}, {"desc": "Open the bench {benchname} with force start", "code": " -f"}, {"desc": "Add custom extension other than default available", "code": " -e vscodevim.vim"}, {"desc": "Sync editor debugger configuration", "code": " -d"}, {"desc": "Use different workdir in editor", "code": " -w /workspace"}]}, "logs": {"examples": [{"desc": "Show logs of Frappe server", "code": ""}, {"desc": "Show logs of Frappe server and follow", "code": " --follow"}, {"desc": "Show logs of NGINX container and follow", "code": " --service nginx --follow"}]}, "shell": {"examples": [{"desc": "Spawn shell for bench {benchname}, user - 'frappe', service - 'frappe'", "code": ""}, {"desc": "Spawn shell for bench {benchname}, user - 'root', service - 'frappe'", "code": " --user root"}, {"desc": "Spawn shell for bench {benchname}, user - 'root', service - 'nginx'", "code": " --service nginx --user nginx"}]}, "start": {"examples": [{"desc": "Start bench {benchname}", "code": ""}, {"desc": "Force recreate bench containers", "code": " --force"}, {"desc": "Sync bench configuration changes", "code": " --sync-config"}, {"desc": "Reconfigure supervisor configuration", "code": " --reconfigure-supervisor"}, {"desc": "Reconfigure common_site_config.json", "code": " --reconfigure-common-site-config"}, {"desc": "Reconfigure workers configuration", "code": " --reconfigure-workers"}, {"desc": "Sync dev packages", "code": " --sync-dev-packages"}, {"desc": "Start with multiple configurations", "code": " --force --sync-config --reconfigure-workers"}]}, "update": {"examples": [{"desc": "Enable SSL.", "code": " --ssl letsencrypt"}, {"desc": "Enable HTTPS using HTTP01 Let's Encrypt challenge certificate.", "code": " --ssl letsencrypt --letsencrypt-preferred-challenge http01 --letsencrypt-email <EMAIL>"}, {"desc": "Enable HTTPS using DNS01 Let's Encrypt challenge certificate.", "code": " --ssl letsencrypt --letsencrypt-preferred-challenge dns01"}, {"desc": "Toggle admin-tools enable.", "code": " --admin-tools enable"}, {"desc": "Toggle admin-tools disable.", "code": " --admin-tools disable"}, {"desc": "Switch to frappe production environment.", "code": " -e prod"}, {"desc": "Switch to frappe development environment.", "code": " --environment dev"}, {"desc": "Enable frappe developer mode.", "code": " --developer-mode enable"}, {"desc": "Disable frappe developer mode.", "code": " --developer-mode disable"}]}, "services": {"start": {"examples": [{"desc": "Start global-db only.", "code": " global-db"}, {"desc": "Start all global services", "code": " all"}]}, "restart": {"examples": [{"desc": "Restart global-db only.", "code": " global-db"}, {"desc": "Restart all global services", "code": " all"}]}, "shell": {"examples": [{"desc": "Shell global-db", "code": " global-db"}, {"desc": "Shell global-nginx-proxy", "code": " global-nginx-proxy"}]}, "stop": {"examples": [{"desc": "Stop global-db", "code": " global-db"}, {"desc": "Stop all services.", "code": " all"}]}}, "ssl": {"delete": {"examples": [{"desc": "Delete {benchname}'s ssl certificate.", "code": ""}]}, "renew": {"examples": [{"desc": "Renew all certificates.", "code": " --all", "benchname": ""}, {"desc": "Renew specific {benchname} ssl certificate.", "code": ""}]}}, "self": {"update": {"examples": [{"desc": "Update fm to the latest version available on pypi", "code": "", "benchname": ""}], "images": {"examples": [{"desc": "Update all frappe required docker images.", "code": "", "benchname": ""}]}}}, "reset": {"examples": [{"desc": "Reset bench {benchname}", "code": ""}]}, "restart": {"examples": [{"desc": "Restart web services only", "code": "--web"}, {"desc": "Restart workers and web services", "code": "--web --workers"}]}}