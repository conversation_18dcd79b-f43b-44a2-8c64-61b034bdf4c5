"""
Editor detection and management utilities for Frappe Manager.

This module provides functionality to detect, validate, and manage different
code editors that can be used with the 'fm code' command.
"""

import shutil
from typing import Dict, List, Optional, Tuple
from frappe_manager import SupportedEditorsEnum


class EditorInfo:
    """Information about a code editor."""

    def __init__(self, name: str, binary_name: str, display_name: str, remote_uri_scheme: str, required_extensions: Optional[List[str]] = None):
        self.name = name
        self.binary_name = binary_name
        self.display_name = display_name
        self.remote_uri_scheme = remote_uri_scheme
        self.required_extensions = required_extensions or []


# Editor configurations
EDITOR_CONFIGS: Dict[SupportedEditorsEnum, EditorInfo] = {
    SupportedEditorsEnum.vscode: EditorInfo(
        name="vscode",
        binary_name="code",
        display_name="Visual Studio Code",
        remote_uri_scheme="vscode-remote",
        required_extensions=["ms-vscode-remote.remote-containers"]
    ),
    SupportedEditorsEnum.cursor: EditorInfo(
        name="cursor",
        binary_name="cursor",
        display_name="Cursor",
        remote_uri_scheme="vscode-remote",
        required_extensions=["ms-vscode-remote.remote-containers"]
    ),
    SupportedEditorsEnum.windsurf: EditorInfo(
        name="windsurf",
        binary_name="windsurf",
        display_name="Windsurf",
        remote_uri_scheme="vscode-remote",
        required_extensions=["ms-vscode-remote.remote-containers"]
    ),
}


def get_editor_info(editor: SupportedEditorsEnum) -> EditorInfo:
    """Get editor information for the specified editor."""
    return EDITOR_CONFIGS[editor]


def is_editor_available(editor: SupportedEditorsEnum) -> bool:
    """Check if the specified editor is available on the system."""
    editor_info = get_editor_info(editor)
    return shutil.which(editor_info.binary_name) is not None


def get_editor_path(editor: SupportedEditorsEnum) -> Optional[str]:
    """Get the full path to the editor binary."""
    editor_info = get_editor_info(editor)
    return shutil.which(editor_info.binary_name)


def detect_available_editors() -> Dict[SupportedEditorsEnum, str]:
    """Detect all available editors on the system."""
    available_editors = {}
    
    for editor in SupportedEditorsEnum:
        path = get_editor_path(editor)
        if path:
            available_editors[editor] = path
    
    return available_editors


def check_editor_extensions(editor: SupportedEditorsEnum) -> Tuple[bool, str]:
    """
    Check if the required extensions are installed for the editor.

    Returns:
        Tuple of (extensions_available, message)
    """
    editor_info = get_editor_info(editor)

    if not editor_info.required_extensions:
        return True, "No extensions required"

    # For now, we can't easily check if extensions are installed without running the editor
    # So we'll provide helpful guidance instead
    missing_extensions = editor_info.required_extensions

    if missing_extensions:
        ext_list = ", ".join(missing_extensions)
        return False, f"{editor_info.display_name} requires these extensions: {ext_list}"

    return True, "Required extensions should be available"


def validate_editor_availability(editor: SupportedEditorsEnum) -> Tuple[bool, str]:
    """
    Validate that the specified editor is available.

    Returns:
        Tuple of (is_available, message)
    """
    editor_info = get_editor_info(editor)

    if is_editor_available(editor):
        return True, f"{editor_info.display_name} is available"
    else:
        return False, f"{editor_info.display_name} binary '{editor_info.binary_name}' is not accessible via CLI"


def get_best_available_editor(preferred_editor: SupportedEditorsEnum) -> Optional[SupportedEditorsEnum]:
    """
    Get the best available editor, preferring the specified one.
    
    Args:
        preferred_editor: The preferred editor to use
        
    Returns:
        The best available editor, or None if no editors are available
    """
    # First try the preferred editor
    if is_editor_available(preferred_editor):
        return preferred_editor
    
    # Fall back to any available editor in order of preference
    fallback_order = [SupportedEditorsEnum.vscode, SupportedEditorsEnum.cursor, SupportedEditorsEnum.windsurf]
    
    for editor in fallback_order:
        if editor != preferred_editor and is_editor_available(editor):
            return editor
    
    return None


def build_editor_command(editor: SupportedEditorsEnum, container_hex: str, workdir: str) -> str:
    """
    Build the command to open the specified editor with remote container.

    Args:
        editor: The editor to use
        container_hex: Hex-encoded container name
        workdir: Working directory path inside container

    Returns:
        Command string to execute (matching original implementation)
    """
    import shlex

    editor_info = get_editor_info(editor)
    editor_path = get_editor_path(editor)

    if not editor_path:
        raise ValueError(f"Editor {editor_info.display_name} is not available")

    # Build the URI exactly like the original implementation
    uri = f"--folder-uri={editor_info.remote_uri_scheme}://attached-container+{container_hex}+{workdir}"

    # Use shlex.join exactly like the original implementation
    return shlex.join([editor_path, uri])
