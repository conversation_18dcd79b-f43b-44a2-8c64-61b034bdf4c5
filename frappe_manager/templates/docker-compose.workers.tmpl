services:
  worker-name:
    image: ghcr.io/rtcamp/frappe-manager-frappe:v0.17.0
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    command: launch_supervisor_service.sh
    networks:
      site-network:
      global-backend-network:
    volumes:
      - fm-sockets:/fm-sockets
      - ./workspace:/workspace
    environment:
      USERID: REPLACE_ME_WITH_CURRENT_USER
      USERGROUP: REPLACE_ME_WITH_CURRENT_USER_GROUP
      SERVICE_NAME: worker-name

volumes:
  fm-sockets:
    name: fm__SITE_NAME_PREFIX__fm-sockets
    external: true

networks:
  site-network:
    name: fm__SITE_NAME_PREFIX__site-network
    external: true
  global-backend-network:
    name: fm-global-backend-network
    external: true
