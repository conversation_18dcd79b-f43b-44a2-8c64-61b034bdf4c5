# Mailpit
location ^~ /mailpit/ {
    auth_basic "Frappe-Manager Admin Tools";
    auth_basic_user_file {{ auth_file }};
    
    chunked_transfer_encoding on;
    proxy_set_header X-NginX-Proxy true;
    proxy_pass http://{{ mailpit_host }}:8025/mailpit/;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_http_version 1.1;
    proxy_redirect off;
    proxy_buffering off;
}

# Adminer
location ^~ /adminer/ {
    auth_basic "Frappe-Manager Admin Tools";
    auth_basic_user_file {{ auth_file }};
    
    proxy_set_header X-Forwarded-For $remote_addr;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Host $host;
    proxy_pass http://{{ adminer_host }}:8080/;
}
