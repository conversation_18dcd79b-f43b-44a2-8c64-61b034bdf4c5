services:
  frappe:
    image: ghcr.io/rtcamp/frappe-manager-frappe:v0.17.0
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    environment:
      USERID: REPLACE_ME_WITH_CURRENT_USER
      USERGROUP: REPLACE_ME_WITH_CURRENT_USER_GROUP
      SERVICE_NAME: frappe
    volumes:
      - fm-sockets:/fm-sockets
      - ./workspace:/workspace
    expose:
      - 80
    labels:
        devcontainer.metadata: '[{ "remoteUser": "frappe"}]'
    networks:
      site-network:
      global-backend-network:

  nginx:
    image: ghcr.io/rtcamp/frappe-manager-nginx:v0.16.1
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    user: REPLACE_ME_WITH_CURRENT_USER:REPLACE_ME_WITH_CURRENT_USER_GROUP
    environment:
      SITENAME: REPLACE_ME_WITH_THE_SITE_NAME
      VIRTUAL_HOST: REPLACE_ME_WITH_SITE_NAME
      VIRTUAL_PORT: 80
      HSTS: 'off'
    volumes:
      - ./workspace:/workspace
      - ./configs/nginx/conf:/etc/nginx
      - ./configs/nginx/logs:/var/log/nginx
      - ./configs/nginx/cache:/var/cache/nginx
      - ./configs/nginx/run:/var/run
      - ./configs/nginx/html:/usr/share/nginx/html
    expose:
      - 80
    networks:
      site-network:
      global-frontend-network:

  socketio:
    image: ghcr.io/rtcamp/frappe-manager-frappe:v0.17.0
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    environment:
      USERID: REPLACE_ME_WITH_CURRENT_USER
      USERGROUP: REPLACE_ME_WITH_CURRENT_USER_GROUP
      SERVICE_NAME: socketio
    expose:
      - 80
    command: launch_supervisor_service.sh
    volumes:
      - fm-sockets:/fm-sockets
      - ./workspace:/workspace
    networks:
      site-network:

  schedule:
    image: ghcr.io/rtcamp/frappe-manager-frappe:v0.17.0
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    environment:
      USERID: REPLACE_ME_WITH_CURRENT_USER
      USERGROUP: REPLACE_ME_WITH_CURRENT_USER_GROUP
      SERVICE_NAME: schedule
    command: launch_supervisor_service.sh
    volumes:
      - fm-sockets:/fm-sockets
      - ./workspace:/workspace
    networks:
      site-network:
      global-backend-network:

  redis-cache:
    image: redis:6.2-alpine
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    volumes:
      - redis-cache-data:/data
    expose:
      - 6379
    networks:
      site-network:

  redis-queue:
    image: redis:6.2-alpine
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    volumes:
      - redis-queue-data:/data
    expose:
      - 6379
    networks:
      site-network:

  redis-socketio:
    image: redis:6.2-alpine
    container_name: REPLACE_ME_WITH_CONTAINER_NAME
    volumes:
       - redis-socketio-data:/data
    expose:
      - 6379
    networks:
      site-network:

volumes:
  fm-sockets:
    name: fm__SITE_NAME_PREFIX__fm-sockets
  redis-socketio-data:
    name: fm__SITE_NAME_PREFIX__redis-socketio-data
  redis-queue-data:
    name: fm__SITE_NAME_PREFIX__redis-queue-data
  redis-cache-data:
    name: fm__SITE_NAME_PREFIX__redis-cache-data

networks:
  site-network:
    name: fm__SITE_NAME_PREFIX__site-network
  global-frontend-network:
    name: fm-global-frontend-network
    external: true
  global-backend-network:
    name: fm-global-backend-network
    external: true
