name: Publish image

on:
  schedule:
    - cron: '0 2 * * *'
  workflow_dispatch:
  push:
    tags:
      - v*

jobs:
  build-and-push:
    name: Build and push image
    strategy:
      matrix:
        os: [self-hosted-arm64,ubuntu-latest]
        platform: [linux/amd64, linux/arm64]
        service_name: [frappe, nginx]
        exclude:
          - os: ubuntu-latest
            platform: linux/arm64
          - os: self-hosted-arm64
            platform: linux/amd64

    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Checkout latest tag when job is schedule or push
        if: github.event_name == 'schedule' || github.event_name == 'push'
        id: set-tag
        run: |
          if [[ "${{ github.event_name }}" == "schedule" ]]; then
            git fetch --all
            git checkout main
            latest_tag=$(git describe --abbrev=0 --tags)
          elif [[ "${{ github.ref == 'refs/tags/v*' }}" == 'true' ]]; then
            latest_tag=${GITHUB_REF/refs\/tags\//}
          else
            latest_tag=$(git describe --abbrev=0 --tags)
          fi

          echo "Checkout $latest_tag"
          git checkout "$latest_tag"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: ${{ matrix.platform }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USER }}
          password: ${{ secrets.GHCR_TOKEN }}

      - name: Set image names (non-frappe)
        if: matrix.service_name != 'frappe'
        run: |
          owner=$( echo '${{ github.repository_owner }}' | tr '[:upper:]' '[:lower:]' )
          tag=$( cat "$GITHUB_WORKSPACE/frappe_manager/images-tag.json" | jq -rc .${{ matrix.service_name }})
          echo "Image tag $tag"
          echo "image_name=ghcr.io/${owner}/frappe-manager-${{ matrix.service_name }}:$(basename ${{ matrix.platform }})-${tag}" >> $GITHUB_ENV

      - name: Set image names
        if: matrix.service_name == 'frappe'
        run: |
          owner=$( echo '${{ github.repository_owner }}' | tr '[:upper:]' '[:lower:]' )
          tag=$( cat "$GITHUB_WORKSPACE/frappe_manager/images-tag.json" | jq -rc .${{ matrix.service_name }})
          echo "prebake_image_name=ghcr.io/${owner}/frappe-manager-prebake:$(basename ${{ matrix.platform }})-${tag}" >> $GITHUB_ENV
          echo "fm_image_name=ghcr.io/${owner}/frappe-manager-${{ matrix.service_name }}:$(basename ${{ matrix.platform }})-${tag}" >> $GITHUB_ENV

      - name: Build and push Docker image (non-frappe)
        if: matrix.service_name != 'frappe'
        uses: docker/build-push-action@v4
        with:
          context: Docker/${{ matrix.service_name }}/.
          push: true
          platforms: ${{ matrix.platform }}
          tags: ${{ env.image_name }}
          provenance: false

      - name: Build and push prebake image
        if: matrix.service_name == 'frappe'
        uses: docker/build-push-action@v4
        with:
          context: Docker/${{ matrix.service_name }}/.
          push: true
          platforms: ${{ matrix.platform }}
          tags: ${{ env.prebake_image_name }}
          target: prebake_image
          provenance: false

      - name: Build and push fm image
        if: matrix.service_name == 'frappe'
        uses: docker/build-push-action@v4
        with:
          context: Docker/${{ matrix.service_name }}/.
          push: true
          platforms: ${{ matrix.platform }}
          tags: ${{ env.fm_image_name }}
          target: fm_image
          provenance: false

  combine:
    name: Combine both platform images
    needs: build-and-push
    strategy:
      matrix:
        os: [ubuntu-latest]
        service_name: [frappe, nginx]

    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Checkout latest tag when job is schedule or push
        if: github.event_name == 'schedule' || github.event_name == 'push'
        id: set-tag
        run: |
          if [[ "${{ github.event_name }}" == "schedule" ]]; then
            git fetch --all
            git checkout main
            latest_tag=$(git describe --abbrev=0 --tags)
          elif [[ "${{ github.ref == 'refs/tags/v*' }}" == 'true' ]]; then
            latest_tag=${GITHUB_REF/refs\/tags\//}
          else
            latest_tag=$(git describe --abbrev=0 --tags)
          fi

          echo "Checkout $latest_tag"
          git checkout "$latest_tag"

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USER }}
          password: ${{ secrets.GHCR_TOKEN }}

      - name: Combine manifest and push
        run: |
          tag=$( cat "$GITHUB_WORKSPACE/frappe_manager/images-tag.json" | jq -rc .${{ matrix.service_name }})
          owner=$( echo '${{ github.repository_owner }}' | tr '[:upper:]' '[:lower:]' )
          
          if [[ "${{ matrix.service_name }}" == "frappe" ]]; then
            # Handle prebake image
            prebake_img="ghcr.io/${owner}/frappe-manager-prebake"
            rm -rf ~/.docker/manifests
            docker pull "$prebake_img:amd64-${tag}"
            docker pull "$prebake_img:arm64-${tag}"
            docker manifest create "$prebake_img":"${tag}" \
              --amend "$prebake_img":amd64-${tag} \
              --amend "$prebake_img":arm64-${tag}
            docker manifest push "$prebake_img:${tag}"
            
            # Handle fm image
            fm_img="ghcr.io/${owner}/frappe-manager-${{ matrix.service_name }}"
            rm -rf ~/.docker/manifests
            docker pull "$fm_img:amd64-${tag}"
            docker pull "$fm_img:arm64-${tag}"
            docker manifest create "$fm_img":"${tag}" \
              --amend "$fm_img":amd64-${tag} \
              --amend "$fm_img":arm64-${tag}
            docker manifest push "$fm_img:${tag}"
          else
            # Handle other services normally
            img="ghcr.io/${owner}/frappe-manager-${{ matrix.service_name }}"
            rm -rf ~/.docker/manifests
            docker pull "$img:amd64-${tag}"
            docker pull "$img:arm64-${tag}"
            docker manifest create "$img":"${tag}" \
              --amend "$img":amd64-${tag} \
              --amend "$img":arm64-${tag}
            docker manifest push "$img:${tag}"
          fi

      - name: Cleanup
        run: |
          rm -rf ~/.docker/manifests
