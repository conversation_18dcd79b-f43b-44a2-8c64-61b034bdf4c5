name: E2E migration testing

on:
  push:
    branches:
      - main
      - develop
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  e2e-migration-from-0_9_0-to-latest:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: true
      matrix:
        python-ver: ["3.11", "3.12"]
        os: [ubuntu-latest]
        # os: [self-hosted-arm64, ubuntu-latest]

    steps:
      - uses: actions/checkout@v4

      - name: Cache Docker images
        id: cache-docker-images
        uses: actions/cache@v3
        with:
          path: /tmp/docker-images
          key: docker-images-${{ runner.os }}-${{ hashFiles('Docker/images-tag.json') }}
          restore-keys: |
            docker-images-${{ runner.os }}-${{ hashFiles('Docker/images-tag.json') }}

      - name: Load cached Docker images
        if: steps.cache-docker-images.outputs.cache-hit == 'true'
        run: |
          mkdir -p /tmp/docker-images
          if [ -f /tmp/docker-images/images.tar ]; then
            docker load < /tmp/docker-images/images.tar
          fi

      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-ver }}

      - name: install frappe from v0.9.0
        run: |
          cd /tmp
          python -m pip install -U git+https://github.com/rtCamp/Frappe-Manager.git@v0.9.0

      - name: fm version
        run: fm --version

      - name: e2e run
        timeout-minutes: 20
        working-directory: test
        run: |
          ./migration_test.sh oldToNew

      - name: Save Docker images
        if: always() && steps.cache-docker-images.outputs.cache-hit != 'true'
        run: |
          mkdir -p /tmp/docker-images
          if [ "$(docker images -q | wc -l)" -gt 0 ]; then
            docker save $(docker images -q) > /tmp/docker-images/images.tar
          fi


      - name: cleanup
        if: always()
        run: |
          python -m pip uninstall -y frappe-manager
          sudo rm -rf ~/frappe

  e2e-migration-from-before_latest-to-latest:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: true
      matrix:
        python-ver: ["3.11", "3.12"]
        os: [ubuntu-latest]
        # os: [self-hosted-arm64, ubuntu-latest]

    steps:
      - uses: actions/checkout@v4

      - name: Cache Docker images
        id: cache-docker-images
        uses: actions/cache@v3
        with:
          path: /tmp/docker-images
          key: docker-images-${{ runner.os }}-${{ hashFiles('Docker/images-tag.json') }}
          restore-keys: |
            docker-images-${{ runner.os }}-${{ hashFiles('Docker/images-tag.json') }}

      - name: Load cached Docker images
        if: steps.cache-docker-images.outputs.cache-hit == 'true'
        run: |
          mkdir -p /tmp/docker-images
          if [ -f /tmp/docker-images/images.tar ]; then
            docker load < /tmp/docker-images/images.tar
          fi

      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-ver }}

      - name: install jq
        run: |
          sudo apt update -y
          sudo apt install -y jq curl

      - name: install frappe previous the latest release
        run: |
          python -m pip install -U git+https://github.com/rtCamp/Frappe-Manager.git@$(curl --silent https://api.github.com/repos/rtCamp/Frappe-Manager/tags | jq -r '.[0].name')

      - name: fm previous version before migration
        id: fm_version_check
        run: |
          FM_VERSION=$(fm --version | cut -d' ' -f2)
          echo "FM_VERSION=$FM_VERSION" >> $GITHUB_ENV
          fm --version

      - name: "[Fix] install specific typer version for 0.16.0"
        if: env.FM_VERSION == '0.16.0'
        run: python -m pip install typer==0.15.4

      - name: e2e run
        timeout-minutes: 30
        working-directory: test
        run: |
          ./migration_test.sh semiNewToNew

      - name: cleanup
        if: always()
        run: |
          python -m pip uninstall -y frappe-manager
          sudo rm -rf ~/frappe
