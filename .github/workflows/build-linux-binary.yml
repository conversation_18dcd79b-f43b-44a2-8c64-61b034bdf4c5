name: Build Linux binary

permissions:
  contents: write
  actions: read

env:
  PYAPP_DOWNLOAD: https://github.com/ofek/pyapp/releases/latest/download/source.tar.gz

on:
  workflow_dispatch: {}
  workflow_call:
  push:
    tags:
      - '*'
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Install Poetry
      uses: snok/install-poetry@v1

    - name: Determine App Version
      run: |
        python .github/scripts/get_version.py

    - name: Setup PyAPP and build
      env:
        PYAPP_UV_ENABLED: 1
        PYAPP_PYTHON_VERSION: '3.11'
        PYAPP_FULL_ISOLATION: 1
        PYAPP_PROJECT_NAME: fm
        PYAPP_EXEC_SPEC: frappe_manager.main:cli_entrypoint
        PYAPP_PIP_EXTRA_ARGS: typer[all] requests psutil ruamel-yaml tomlkit certbot pydantic email-validator jinja2 certbot-dns-cloudflare ngrok passlib
        PYAPP_DISTRIBUTION_EMBED: 1
      run: |
        poetry build
        curl ${PYAPP_DOWNLOAD} -Lo pyapp-source.tar.gz
        tar -xzf pyapp-source.tar.gz
        mv pyapp-v* pyapp-latest
        cd pyapp-latest
        cargo build --release
        mv target/release/pyapp ../fm && chmod +x ../fm

    - name: Upload Artifact
      uses: actions/upload-artifact@v4
      with:
        name: frappe_manager-${{ env.PYAPP_PROJECT_VERSION }}-linux-amd64
        path: |
          fm
          LICENSE
          README.md
        retention-days: 5

    - name: Create release zip
      if: ${{ github.event.release && github.event.action == 'published' }}
      run: zip frappe_manager-${{ env.PYAPP_PROJECT_VERSION }}-linux-amd64.zip "frappe_manager" "LICENSE" "README.md"

    - name: Attach files to release
      uses: softprops/action-gh-release@v2
      if: ${{ github.event.release && github.event.action == 'published' }}
      with:
        files: frappe_manager-${{ env.PYAPP_PROJECT_VERSION }}-linux-amd64.zip
